import { Resend } from 'resend';

// Determine if we're in production or development
const isProduction = process.env.NODE_ENV === 'production';

// Get the application domain
const domain = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';

// Initialize Resend with the API key
const resendApiKey = process.env.RESEND_API_KEY;
console.log('Resend API Key:', resendApiKey ? `${resendApiKey.substring(0, 5)}...${resendApiKey.substring(resendApiKey.length - 5)}` : 'undefined');
const resend = new Resend(resendApiKey);

interface EmailOptions {
  to: string | string[];
  subject: string;
  html: string;
  from?: string;
  text?: string;
  cc?: string | string[];
  bcc?: string | string[];
  replyTo?: string;
  attachments?: Array<{
    filename: string;
    content: Buffer;
  }>;
}

/**
 * Send an email using Resend
 *
 * @param options Email options
 * @returns The result of the email sending operation
 */
export async function sendEmail(options: EmailOptions) {
  try {
    const { to, subject, html, from, text, cc, bcc, replyTo, attachments } = options;

    // Use the default from address if not provided
    const fromAddress = from || process.env.EMAIL_FROM || '<EMAIL>';

    console.log('sendEmail function called with:');
    console.log('- To:', to);
    console.log('- Subject:', subject);
    console.log('- From:', fromAddress);
    console.log('- Environment:', process.env.NODE_ENV);
    console.log('- EMAIL_TESTING:', process.env.EMAIL_TESTING);
    console.log('- isProduction:', isProduction);

    // In development, log the email instead of sending it unless EMAIL_TESTING is true
    if (!isProduction && process.env.EMAIL_TESTING !== 'true') {
      console.log('Email would be sent in production (EMAIL_TESTING is not true):');
      console.log('From:', fromAddress);
      console.log('To:', to);
      console.log('Subject:', subject);
      console.log('HTML:', html.substring(0, 500) + (html.length > 500 ? '...' : ''));

      // Simulate a delay
      await new Promise(resolve => setTimeout(resolve, 500));

      return {
        id: 'dev_' + Date.now(),
        from: fromAddress,
        to,
        subject,
      };
    }

    console.log('Proceeding to send actual email (EMAIL_TESTING is true or in production)');

    // Send the email
    console.log('Sending email via Resend API...');
    const emailPayload = {
      from: fromAddress,
      to: Array.isArray(to) ? to : [to],
      subject,
      html,
      text: text || '',
      ...(cc && { cc: Array.isArray(cc) ? cc : [cc] }),
      ...(bcc && { bcc: Array.isArray(bcc) ? bcc : [bcc] }),
      ...(replyTo && { reply_to: replyTo }),
      ...(attachments && { attachments }),
    };

    console.log('Email payload:', JSON.stringify({
      ...emailPayload,
      html: emailPayload.html.length > 100 ? emailPayload.html.substring(0, 100) + '...' : emailPayload.html
    }, null, 2));

    const result = await resend.emails.send(emailPayload);
    console.log('Resend API response:', result);

    if (result.error) {
      console.error('Resend API error:', result.error);
      throw new Error(`Failed to send email: ${result.error.message}`);
    }

    console.log('Email sent successfully with ID:', result.data?.id);
    return result.data;
  } catch (error) {
    console.error('Error sending email:', error);
    throw error;
  }
}

/**
 * Send a team invitation email
 *
 * @param email Recipient email address
 * @param teamName Name of the team
 * @param inviterName Name of the person who sent the invitation
 * @param role Role being offered in the team
 * @param token Invitation token
 * @returns The result of the email sending operation
 */
export async function sendTeamInvitationEmail(
  email: string,
  teamName: string,
  inviterName: string,
  role: string,
  token: string
) {
  console.log('Sending team invitation email to:', email);
  console.log('Team name:', teamName);
  console.log('Inviter name:', inviterName);
  console.log('Role:', role);
  console.log('Token:', token);
  console.log('Domain:', domain);
  console.log('EMAIL_TESTING:', process.env.EMAIL_TESTING);
  console.log('RESEND_API_KEY exists:', !!process.env.RESEND_API_KEY);

  // Format the role for display (e.g., ORGANIZER_ADMIN -> Organizer Admin)
  const formattedRole = role
    .replace('ORGANIZER_', '')
    .toLowerCase()
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');

  // Create the invitation link
  const invitationLink = `${domain}/dashboard/organizer/team/invitations?token=${token}`;
  console.log('Invitation link:', invitationLink);

  // Create the email HTML
  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <div style="background-color: #f9fafb; padding: 20px;">
        <h2 style="color: #111827;">Team Invitation</h2>
        <p>Hello,</p>
        <p>You have been invited by <strong>${inviterName}</strong> to join the team <strong>${teamName}</strong> as a <strong>${formattedRole}</strong>.</p>
        <p>Click the button below to accept or decline this invitation:</p>
        <div style="text-align: center; margin: 30px 0;">
          <a href="${invitationLink}" style="background-color: #4f46e5; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">View Invitation</a>
        </div>
        <p>If you didn't expect this invitation, you can safely ignore this email.</p>
        <p>This invitation will expire in 7 days.</p>
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
          <p style="font-size: 14px; color: #6b7280;">Thank you for using our platform.</p>
        </div>
      </div>
    </div>
  `;

  try {
    console.log('Attempting to send email...');
    const result = await sendEmail({
      to: email,
      subject: `Invitation to join ${teamName}`,
      html,
    });
    console.log('Email sent successfully:', result);
    return result;
  } catch (error) {
    console.error('Error sending team invitation email:', error);
    throw error;
  }
}
