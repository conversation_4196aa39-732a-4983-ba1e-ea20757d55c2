// components/Navbar.js
"use client";

import Link from 'next/link';
import { useSession } from "next-auth/react";

import { Button } from "@/components/ui/button";
import { User } from "lucide-react";

const Navbar = () => {
  const { data: session, status } = useSession();



  return (
    <nav className="flex items-center justify-between flex-wrap bg-teal-500 p-6">
      <div className="flex items-center flex-shrink-0 text-white mr-6">
        <Link href="/">
          <span className="font-semibold text-xl tracking-tight cursor-pointer">Logo</span>
        </Link>
      </div>
      <div className="block lg:hidden">
        {/* Mobile menu button */}
      </div>
      <div className="w-full block flex-grow lg:flex lg:items-center lg:w-auto">
        <div className="text-sm lg:flex-grow">
          {/* Navigation Links */}
          <Link href="/events">
            <span className="block mt-4 lg:inline-block lg:mt-0 text-teal-200 hover:text-white mr-4 cursor-pointer">
              Events
            </span>
          </Link>
          <Link href="/vendors">
            <span className="block mt-4 lg:inline-block lg:mt-0 text-teal-200 hover:text-white mr-4 cursor-pointer">
              Vendors
            </span>
          </Link>

          {/* Simple Authentication Links */}
          {status === 'loading' ? (
            <div className="inline-flex space-x-4 mt-4 lg:mt-0">
              <div className="w-16 h-6 bg-teal-400 animate-pulse rounded"></div>
            </div>
          ) : session?.user ? (
            <div className="inline-flex items-center space-x-4 mt-4 lg:mt-0">
              <Link href="/dashboard">
                <Button variant="outline" size="sm" className="text-teal-600 border-teal-200 hover:bg-teal-50">
                  <User className="h-4 w-4 mr-2" />
                  Dashboard
                </Button>
              </Link>
            </div>
          ) : (
            <>
              <Link href="/login">
                <span className="block mt-4 lg:inline-block lg:mt-0 text-teal-200 hover:text-white mr-4 cursor-pointer">
                  Login
                </span>
              </Link>
              <Link href="/auth/register">
                <span className="block mt-4 lg:inline-block lg:mt-0 text-teal-200 hover:text-white mr-4 cursor-pointer">
                  Signup
                </span>
              </Link>
            </>
          )}
        </div>
        <div>
          <input type="search" placeholder="Search..." className="search-bar" />
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
