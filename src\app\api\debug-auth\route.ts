import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { compare } from 'bcryptjs';
import { getUserByEmail } from '@/data/user';

export async function POST(request: NextRequest) {
  try {
    const { email, password } = await request.json();
    
    console.log('Debug auth called with:', { email, hasPassword: !!password });
    
    // Step 1: Check if user exists
    const user = await getUserByEmail(email);
    if (!user) {
      return NextResponse.json({
        success: false,
        step: 'user_lookup',
        error: 'User not found',
        email
      });
    }
    
    console.log('User found:', { id: user.id, email: user.email, hasPassword: !!user.password });
    
    // Step 2: Check password
    if (!user.password) {
      return NextResponse.json({
        success: false,
        step: 'password_check',
        error: 'User has no password (OAuth account?)',
        user: {
          id: user.id,
          email: user.email,
          role: user.role,
          emailVerified: user.emailVerified
        }
      });
    }
    
    // Step 3: Verify password
    const isPasswordValid = await compare(password, user.password);
    console.log('Password validation result:', isPasswordValid);
    
    if (!isPasswordValid) {
      return NextResponse.json({
        success: false,
        step: 'password_validation',
        error: 'Invalid password',
        user: {
          id: user.id,
          email: user.email,
          role: user.role
        }
      });
    }
    
    // Step 4: Check email verification
    if (!user.emailVerified) {
      return NextResponse.json({
        success: false,
        step: 'email_verification',
        error: 'Email not verified',
        user: {
          id: user.id,
          email: user.email,
          role: user.role,
          emailVerified: user.emailVerified
        }
      });
    }
    
    // All checks passed
    return NextResponse.json({
      success: true,
      message: 'All authentication checks passed',
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
        emailVerified: user.emailVerified,
        createdAt: user.createdAt
      }
    });
    
  } catch (error) {
    console.error('Debug auth error:', error);
    return NextResponse.json({
      success: false,
      step: 'exception',
      error: 'Debug authentication failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Debug auth endpoint - use POST with email and password',
    environment: {
      nodeEnv: process.env.NODE_ENV,
      hasDbUrl: !!process.env.DATABASE_URL,
      hasAuthSecret: !!process.env.AUTH_SECRET,
      nextAuthUrl: process.env.NEXTAUTH_URL
    }
  });
}
