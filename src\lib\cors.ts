import { NextResponse } from 'next/server';
import { NextRequest } from 'next/server';

// Define allowed origins
const allowedOrigins = [
  // Development origins
  'http://localhost:3000',
  'http://localhost:3001',
  'http://localhost:3002',
  'http://************:3000',
  'http://************:3001',
  'http://************:3002',
  // Production origins
  'https://event-sx-jxel.vercel.app',
  'https://api.event-sx-jxel.vercel.app',
  'https://admin.event-sx-jxel.vercel.app'
];

// Check if we're in development mode
const isDevelopment = process.env.NODE_ENV === 'development';

/**
 * Adds CORS headers to a response
 * @param response The NextResponse object
 * @param request The NextRequest object
 * @param methods HTTP methods to allow
 * @returns The response with CORS headers
 */
export function addCorsHeaders(
  response: NextResponse,
  request: NextRequest,
  methods: string = 'GET, OPTIONS'
): NextResponse {
  const origin = request.headers.get('origin');

  if (isDevelopment) {
    // In development, allow all origins
    response.headers.set('Access-Control-Allow-Origin', origin || '*');
  } else {
    // In production, only allow specific origins
    if (origin && allowedOrigins.includes(origin)) {
      response.headers.set('Access-Control-Allow-Origin', origin);
    }
  }

  // Set other CORS headers
  response.headers.set('Access-Control-Allow-Methods', methods);
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-API-Key, Cache-Control');
  response.headers.set('Access-Control-Expose-Headers', 'X-RateLimit-Limit, X-RateLimit-Remaining, X-RateLimit-Reset');

  // Add security headers
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-XSS-Protection', '1; mode=block');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');

  return response;
}

/**
 * Creates a response for OPTIONS requests (CORS preflight)
 * @param methods HTTP methods to allow
 * @param request The NextRequest object (optional)
 * @returns A 204 response with CORS headers
 */
export function corsPreflightResponse(methods: string = 'GET, OPTIONS', request?: NextRequest): NextResponse {
  const response = new NextResponse(null, { status: 204 });

  if (request) {
    const origin = request.headers.get('origin');

    if (isDevelopment) {
      // In development, allow all origins
      response.headers.set('Access-Control-Allow-Origin', origin || '*');
    } else {
      // In production, only allow specific origins
      if (origin && allowedOrigins.includes(origin)) {
        response.headers.set('Access-Control-Allow-Origin', origin);
      }
    }
  } else {
    // Fallback if request is not provided
    if (isDevelopment) {
      response.headers.set('Access-Control-Allow-Origin', '*');
    }
  }

  response.headers.set('Access-Control-Allow-Methods', methods);
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-API-Key, Cache-Control');
  response.headers.set('Access-Control-Max-Age', '86400'); // 24 hours

  // Add security headers
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-XSS-Protection', '1; mode=block');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');

  return response;
}
