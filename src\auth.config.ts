import Credentials from "next-auth/providers/credentials"
import type { NextAuthOptions } from "next-auth"
import { loginSchema } from "./schemas"
import { getUserByEmail } from "./data/user"
import { compare } from "bcryptjs"
import Github from "next-auth/providers/github"
import GoogleProvider from "./lib/auth/google-provider"
import { db } from "./lib/prisma"

// Log environment variables for debugging (will be removed in production)
console.log('Environment variables check:', {
  NEXTAUTH_URL: process.env.NEXTAUTH_URL,
  GOOGLE_CLIENT_ID: process.env.GOOGLE_CLIENT_ID ? 'set' : 'not set',
  GOOGLE_CLIENT_SECRET: process.env.GOOGLE_CLIENT_SECRET ? 'set' : 'not set'
});

const authConfig: NextAuthOptions = {
  // Use custom callbacks for OAuth providers
  callbacks: {
    async signIn({ user, account, profile, email, credentials }) {
      // Allow all sign-ins to proceed
      return true;
    },
    async redirect({ url, baseUrl }) {
      // If the URL is an absolute URL, check if it's from the same origin
      if (url.startsWith('http')) {
        const urlObj = new URL(url);
        const baseUrlObj = new URL(baseUrl);

        // If the URL is from the same origin, allow it
        if (urlObj.origin === baseUrlObj.origin) {
          return url;
        }

        // Otherwise, redirect to the base URL
        return baseUrl;
      }

      // If the URL is a relative URL, prepend the base URL
      if (url.startsWith('/')) {
        return `${baseUrl}${url}`;
      }

      // Otherwise, redirect to the base URL
      return baseUrl;
    }
  },
  providers: [
    Github({
      clientId: process.env.GITHUB_CLIENT_ID ?? "",
      clientSecret: process.env.GITHUB_CLIENT_SECRET ?? "",
    }),
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID ?? "",
      clientSecret: process.env.GOOGLE_CLIENT_SECRET ?? "",
      allowDangerousEmailAccountLinking: true // Allow linking accounts with the same email
    }),
    Credentials({
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        console.log("Credentials authorize function called with:",
          credentials ? { email: credentials.email, passwordProvided: !!credentials.password } : "no credentials");

        try {
          const validatedFields = loginSchema.safeParse(credentials)

          if (!validatedFields.success) {
            console.log("Validation failed:", validatedFields.error);
            return null;
          }

          const { email, password } = validatedFields.data
          console.log("Attempting to find user with email:", email);

          const user = await getUserByEmail(email)
          if (!user) {
            console.log("No user found with email:", email);
            return null;
          }

          console.log("User found:", { id: user.id, email: user.email, role: user.role, hasPassword: !!user.password });

          if (!user.password) {
            console.log("User found but has no password (OAuth account?)");
            return null;
          }

          console.log("User found, checking password");
          console.log("[Authorize] Password received for comparison:", `"${password}"`);
          console.log("[Authorize] Stored hash from DB for user:", user.password);
          const isPasswordValid = await compare(password, user.password)

          if (!isPasswordValid) {
            console.log("Password validation failed");
            return null;
          }

          console.log("Password valid, checking if user is a partner");
          // Check if this user is a partner
          const partner = await db.partner.findUnique({
            where: { userId: user.id },
          });

          if (partner) {
            console.log("User is a partner");
            // If user is a partner, update their role if needed
            if (user.role !== 'PARTNER') {
              console.log("Updating user role to PARTNER");
              await db.user.update({
                where: { id: user.id },
                data: { role: 'PARTNER' }
              });

              // Return updated user with PARTNER role
              console.log("Returning user with updated PARTNER role");
              return {
                ...user,
                accessToken: user.accessToken === null ? undefined : user.accessToken,
                accountBalance: typeof user.accountBalance === 'number' ? String(user.accountBalance) : undefined,
                role: 'PARTNER'
              };
            }
          }

          console.log("Authentication successful, returning user with role:", user.role);
          return {
            ...user,
            accessToken: user.accessToken === null ? undefined : user.accessToken,
            accountBalance: typeof user.accountBalance === 'number' ? String(user.accountBalance) : undefined,
          };
        } catch (error) {
          console.error("Error in authorize function:", error);
          if (error instanceof Error) {
            console.error("Error message:", error.message);
            console.error("Error stack:", error.stack);
          }
          return null;
        }
      }
    })
  ]
};

export default authConfig;