import NextAuth from 'next-auth'
import { fullAuthConfig } from '@/auth'
import { NextRequest, NextResponse } from 'next/server'

// Create the auth route handlers with the full config
const handler = NextAuth(fullAuthConfig)

// Wrap handlers with error handling
async function wrappedHandler(req: NextRequest) {
  try {
    console.log(`NextAuth ${req.method} request to:`, req.url);
    const response = await handler(req);
    console.log('NextAuth response status:', response?.status);
    return response;
  } catch (error) {
    console.error('NextAuth handler error:', error);
    return NextResponse.json(
      { error: 'Authentication service error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

// Export the wrapped handlers
export { wrappedHandler as GET, wrappedHandler as POST }