import { NextApiRequest, NextApiResponse } from 'next';
import { Server as SocketIOServer } from 'socket.io';
import type { Socket } from 'socket.io';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/auth';

// Define socket data interface for TypeScript
interface SocketData {
  authenticated?: boolean;
  user?: {
    id: string;
    role?: string;
    [key: string]: any;
  };
}

// Define socket with data type
type TypedSocket = Socket & {
  data: SocketData;
}

/**
 * Socket.IO server initialization
 *
 * This endpoint initializes the Socket.IO server and attaches it to the Next.js server.
 * It handles authentication and sets up event handlers for real-time communication.
 */
export default async function handler(_req: NextApiRequest, res: NextApiResponse) {
  // Check if Socket.IO server is already initialized
  if ((res.socket as any).server.io) {
    console.log('Socket.IO is already running');
    res.end();
    return;
  }

  console.log('Initializing Socket.IO server...');

  try {
    // Create new Socket.IO server
    const io = new SocketIOServer((res.socket as any).server, {
      path: '/api/socket',
      // Add CORS configuration if needed
      cors: {
        origin: [process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000', 'https://event-sx-jxel.vercel.app'],
        methods: ['GET', 'POST'],
        credentials: true,
      },
    });

    // Store the Socket.IO server instance on the server object
    (res.socket as any).server.io = io;

    // Middleware for authentication
    io.use(async (socket: TypedSocket, next: (err?: Error) => void) => {
      try {
        // Get token from handshake auth
        const token = socket.handshake.auth.token;

        // If no token is provided, allow connection but with limited access
        if (!token) {
          console.log('Socket connection without authentication token');
          socket.data.authenticated = false;
          socket.data.user = { id: 'anonymous', role: 'GUEST' };

          // Join public room
          socket.join('public');
          return next();
        }

        try {
          // Verify session (this is a simplified example)
          // In a real implementation, you would verify the token properly
          const session = await getServerSession(
            { headers: { cookie: `next-auth.session-token=${token}` } } as any,
            { getHeader: () => {}, setHeader: () => {} } as any,
            authOptions
          );

          if (!session?.user) {
            console.log('Invalid session token, allowing connection with limited access');
            socket.data.authenticated = false;
            socket.data.user = { id: 'anonymous', role: 'GUEST' };

            // Join public room
            socket.join('public');
            return next();
          }

          // Store user data in socket
          socket.data.authenticated = true;
          socket.data.user = session.user;

          // Join user-specific room
          socket.join(`user:${session.user.id}`);

          // Join role-specific room
          if (session.user.role) {
            socket.join(`role:${session.user.role.toLowerCase()}`);
          }

          // If user is a vendor, join vendor-specific room
          if (session.user.role === 'VENDOR') {
            socket.join(`vendor:${session.user.id}`);
          }

          console.log(`Authenticated socket connection for user: ${session.user.id}, role: ${session.user.role}`);
          next();
        } catch (sessionError) {
          console.error('Session verification error:', sessionError);
          socket.data.authenticated = false;
          socket.data.user = { id: 'anonymous', role: 'GUEST' };

          // Join public room
          socket.join('public');
          next();
        }
      } catch (error) {
        console.error('Socket authentication error:', error);
        // Allow connection even if authentication fails, but with limited access
        socket.data.authenticated = false;
        socket.data.user = { id: 'anonymous', role: 'GUEST' };

        // Join public room
        socket.join('public');
        next();
      }
    });

    // Connection event handler
    io.on('connection', (socket: TypedSocket) => {
      console.log(`Client connected: ${socket.id}`);

      // Send initial metrics
      socket.emit('metrics_update', {
        concurrentUsers: io.engine.clientsCount,
        transactionsPerMinute: global.lastTransactionTimestamps?.length || 0,
      });

      // Disconnect event handler
      socket.on('disconnect', () => {
        console.log(`Client disconnected: ${socket.id}`);
      });
    });

    console.log('Socket.IO server initialized successfully');
  } catch (error) {
    console.error('Error initializing Socket.IO server:', error);
  }

  res.end();
}
