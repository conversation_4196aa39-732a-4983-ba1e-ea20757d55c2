"use client";

import React from 'react';
import { ThemeProvider } from '@/components/providers/theme-provider';
import { NotificationProvider } from '@/contexts/notification-context';
import { SubscriptionProvider } from '@/contexts/subscription-context';
import { Toaster } from '@/components/ui/sonner';
import { SessionProvider } from 'next-auth/react';
import { NextAuthErrorHandler } from '@/components/auth/next-auth-error-handler';
import { ThemeScript } from '@/components/providers/theme-script';

function ProvidersComponent({ children }: { children: React.ReactNode }) {
  // Add error boundary to catch and handle any session-related errors
  return (
    <SessionProvider
      basePath="/api/auth"
      // Add refetchInterval to periodically refresh the session
      refetchInterval={30 * 60} // Refresh every 30 minutes
      refetchOnWindowFocus={true} // Refresh when window gets focus
      baseUrl={process.env.NEXTAUTH_URL || process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"}
    >
      {/* Add error handler for Next-auth client-side errors */}
      <NextAuthErrorHandler />
      <ThemeProvider attribute="class" defaultTheme="light" enableSystem>
        <ThemeScript />
        <NotificationProvider>
          <SubscriptionProvider>
            {children}
            <Toaster />
          </SubscriptionProvider>
        </NotificationProvider>
      </ThemeProvider>
    </SessionProvider>
  );
}

// Export both as default and named export
export { ProvidersComponent as Providers };
export default ProvidersComponent;
