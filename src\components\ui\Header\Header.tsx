
"use client";

import React, { useState, useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import { FiMenu, FiX } from "react-icons/fi";
import { useSession } from "next-auth/react";
import { Button } from "@/components/ui/button";
import { User } from "lucide-react";

import logo from "../../../../public/images/quick.png";
import Modal from "../modal";
import { RegisterForm } from "@/components/auth/register-form";
import { LoginForm } from "@/components/auth/login-form";

const Header = () => {
  const { data: session, status } = useSession();
  const [showModal, setShowModal] = useState(false);
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);

  const handleModalOpen = () => setShowModal(true);
  const handleModalClose = () => setShowModal(false);
  const handleLoginModalOpen = () => setShowLoginModal(true);
  const handleLoginModalClose = () => setShowLoginModal(false);
  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);

  // Simple authenticated menu - just dashboard
  const renderAuthenticatedMenu = () => (
    <div className="flex items-center space-x-3 ml-4">
      <Link href="/dashboard">
        <Button variant="outline" size="sm">
          <User className="h-4 w-4 mr-2" />
          Dashboard
        </Button>
      </Link>
    </div>
  );

  // Simple guest menu - just login/signup
  const renderGuestMenu = () => (
    <div className="flex items-center space-x-3 ml-4">
      <Link href="/login">
        <span className="px-4 py-2 bg-white text-blue-600 border border-blue-600 rounded-md hover:bg-blue-50 transition-colors duration-200 font-medium inline-block">
          Log In
        </span>
      </Link>
      <Link href="/auth/register">
        <span className="px-4 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600 transition-colors duration-200 font-medium shadow-sm inline-block">
          Sign Up
        </span>
      </Link>
    </div>
  );

  // Simple mobile authenticated menu - just dashboard
  const renderMobileAuthenticatedMenu = () => (
    <div className="flex flex-col space-y-3 mt-auto pt-6">
      <Link href="/dashboard" onClick={toggleMenu}>
        <span className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200 font-medium w-full inline-block text-center">
          <User className="h-4 w-4 mr-2 inline" />
          Dashboard
        </span>
      </Link>
    </div>
  );

  // Simple mobile guest menu - just login/signup
  const renderMobileGuestMenu = () => (
    <div className="flex flex-col space-y-3 mt-auto pt-6">
      <Link href="/login" onClick={toggleMenu}>
        <span className="px-4 py-2 bg-white text-blue-600 border border-blue-600 rounded-md hover:bg-blue-50 transition-colors duration-200 font-medium w-full inline-block text-center">
          Log In
        </span>
      </Link>
      <Link href="/auth/register" onClick={toggleMenu}>
        <span className="px-4 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600 transition-colors duration-200 font-medium shadow-sm w-full inline-block text-center">
          Sign Up
        </span>
      </Link>
    </div>
  );

  useEffect(() => {
    const handleScroll = () => setScrolled(window.scrollY > 0);
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);



  return (
    <header
      className={`w-full h-[80px] px-6 bg-white shadow-md flex justify-between items-center transition-all duration-300 ${
        scrolled ? "fixed top-0 left-0 z-50 backdrop-blur-sm bg-white/95" : ""
      }`}
    >
      <div className="flex items-center">
        <Link href="/" className="flex items-center">
          <div className="relative overflow-hidden rounded-lg">
            <Image
              src={logo}
              alt="Quick Time Technologies Zambia Ltd"
              width={120}
              height={60}
              className="object-contain transition-transform hover:scale-105"
              priority
            />
          </div>

        </Link>
      </div>

      {/* Desktop Nav */}
      <nav className="hidden md:flex items-center space-x-6">
        <Link href="/events">
          <span className="text-gray-700 hover:text-orange-500 font-medium transition-colors duration-200">Events</span>
        </Link>
        <Link href="/partners">
          <span className="text-gray-700 hover:text-orange-500 font-medium transition-colors duration-200">Partners</span>
        </Link>

        <Link href="/vendors">
          <span className="text-gray-700 hover:text-orange-500 font-medium transition-colors duration-200">Vendors</span>
        </Link>
        <Link href="/about">
          <span className="text-gray-700 hover:text-orange-500 font-medium transition-colors duration-200">About</span>
        </Link>
        <Link href="/blog">
          <span className="text-gray-700 hover:text-orange-500 font-medium transition-colors duration-200">Blog</span>
        </Link>
        {/* Simple Authentication Menu */}
        {status === 'loading' ? (
          <div className="flex items-center space-x-3 ml-4">
            <div className="w-20 h-8 bg-gray-200 animate-pulse rounded"></div>
          </div>
        ) : session?.user ? (
          renderAuthenticatedMenu()
        ) : (
          renderGuestMenu()
        )}
      </nav>

      {/* Mobile Menu Button - Visible only on small screens */}
      <div className="md:hidden">
        <button
          onClick={toggleMenu}
          aria-label="Toggle Menu"
          className="p-2 rounded-full hover:bg-gray-100 transition-colors duration-200"
        >
          {isMenuOpen ? <FiX size={24} color="#FF5733" /> : <FiMenu size={24} color="#FF5733" />}
        </button>
      </div>

      {/* Mobile Slide-in Menu */}
      <div
        className={`fixed top-0 right-0 h-full w-[280px] bg-white shadow-xl z-50 transform transition-transform duration-300 ease-in-out ${isMenuOpen ? 'translate-x-0 animate-slideInMenu' : 'translate-x-full'}`}
      >
        <div className="flex flex-col h-full p-6 overflow-y-auto">
          <div className="flex justify-between items-center mb-8">
            <Image
              src={logo}
              alt="Quick Time Technologies Zambia Ltd"
              width={80}
              height={40}
              className="object-contain"
            />
            <button
              onClick={toggleMenu}
              className="p-2 rounded-full hover:bg-gray-100 transition-colors duration-200"
            >
              <FiX size={20} color="#FF5733" />
            </button>
          </div>

          <nav className="flex flex-col space-y-4">
            <Link href="/events" onClick={toggleMenu}>
              <span className="text-gray-700 hover:text-orange-500 font-medium transition-colors duration-200 block py-2">Events</span>
            </Link>
            <Link href="/partners" onClick={toggleMenu}>
              <span className="text-gray-700 hover:text-orange-500 font-medium transition-colors duration-200 block py-2">Partners</span>
            </Link>
            <Link href="/organizers" onClick={toggleMenu}>
              <span className="text-gray-700 hover:text-orange-500 font-medium transition-colors duration-200 block py-2">Organizers</span>
            </Link>
            <Link href="/vendors" onClick={toggleMenu}>
              <span className="text-gray-700 hover:text-orange-500 font-medium transition-colors duration-200 block py-2">Vendors</span>
            </Link>
            <Link href="/about" onClick={toggleMenu}>
              <span className="text-gray-700 hover:text-orange-500 font-medium transition-colors duration-200 block py-2">About</span>
            </Link>
            <Link href="/blog" onClick={toggleMenu}>
              <span className="text-gray-700 hover:text-orange-500 font-medium transition-colors duration-200 block py-2">Blog</span>
            </Link>
          </nav>

          {/* Simple Mobile Authentication Menu */}
          {status === 'loading' ? (
            <div className="flex flex-col space-y-3 mt-auto pt-6">
              <div className="w-full h-10 bg-gray-200 animate-pulse rounded"></div>
            </div>
          ) : session?.user ? (
            renderMobileAuthenticatedMenu()
          ) : (
            renderMobileGuestMenu()
          )}
        </div>
      </div>

      {/* Overlay when menu is open */}
      {isMenuOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 animate-fadeIn"
          onClick={toggleMenu}
        ></div>
      )}

      {/* Login and Register Modals removed */}
      {showLoginModal && (
        <Modal onClose={handleLoginModalClose}>
          <LoginForm />
        </Modal>
      )}

      {/* Register Modal */}
      {showModal && (
        <Modal onClose={handleModalClose}>
          <RegisterForm />
        </Modal>
      )}
    </header>
  );
};

export default Header;
