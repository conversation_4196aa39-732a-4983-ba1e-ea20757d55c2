/**
 * Template processor for email templates
 * Replaces dynamic variables with actual data
 */
import { db } from '@/lib/prisma';

interface TemplateData {
  user?: {
    name?: string;
    email?: string;
    [key: string]: any;
  };
  organizer?: {
    name?: string;
    email?: string;
    [key: string]: any;
  };
  event?: {
    title?: string;
    date?: string;
    time?: string;
    location?: string;
    description?: string;
    category?: string;
    [key: string]: any;
  };
  ticket?: {
    type?: string;
    price?: string;
    quantity?: number;
    [key: string]: any;
  };
  order?: {
    number?: string;
    date?: string;
    total?: string;
    [key: string]: any;
  };
  subscriber?: {
    firstName?: string;
    lastName?: string;
    email?: string;
    id?: string;
    [key: string]: any;
  };
  subscribers?: Array<{
    id: string;
    name?: string;
    email: string;
    [key: string]: any;
  }>;
  [key: string]: any;
}

/**
 * Process a template string by replacing variables with actual data
 * @param template The template string containing variables like {{user.name}}
 * @param data The data object containing values for the variables
 * @returns The processed template with variables replaced by actual values
 */
export function processTemplate(template: string, data: TemplateData): string {
  // Replace date variable with current date
  const now = new Date();
  const formattedDate = now.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  // Add current date to data
  const templateData = {
    ...data,
    date: formattedDate
  };

  // Replace variables in the template
  return template.replace(/\{\{([^}]+)\}\}/g, (match, path) => {
    // Special case for subscriber.ID.property format
    const subscriberMatch = path.trim().match(/^subscriber\.([a-zA-Z0-9-]+)\.(.+)$/);
    if (subscriberMatch) {
      const subscriberId = subscriberMatch[1];
      const property = subscriberMatch[2];

      // Find the subscriber in the subscribers array
      if (templateData.subscribers && Array.isArray(templateData.subscribers)) {
        const subscriber = templateData.subscribers.find((s: any) => s.id === subscriberId);
        if (subscriber) {
          if (property === 'name') {
            return subscriber.name || 'Subscriber';
          } else if (property === 'email') {
            return subscriber.email || '<EMAIL>';
          }
        }
      }

      // If we can't find the subscriber, use the current subscriber
      if (templateData.subscriber) {
        if (property === 'name') {
          return templateData.subscriber.firstName ?
            `${templateData.subscriber.firstName} ${templateData.subscriber.lastName || ''}`.trim() :
            'Subscriber';
        } else if (property === 'email') {
          return templateData.subscriber.email || '<EMAIL>';
        }
      }

      return match; // Keep the original placeholder if we can't resolve it
    }

    // Special case for event.ID.property format
    const eventMatch = path.trim().match(/^event\.([a-zA-Z0-9-]+)\.(.+)$/);
    if (eventMatch) {
      const eventId = eventMatch[1];
      const property = eventMatch[2];

      // Try to find the event in the database
      return db.event.findUnique({
        where: { id: eventId }
      }).then(event => {
        if (event) {
          if (property === 'title') {
            return event.title || 'Event';
          } else if (property === 'description') {
            return event.description || 'No description available';
          } else if (property === 'location') {
            return event.location || 'TBD';
          } else if (property === 'venue') {
            return event.venue || 'TBD';
          } else if (property === 'startDate' || property === 'date') {
            return event.startDate ? new Date(event.startDate).toLocaleDateString('en-US', {
              year: 'numeric',
              month: 'long',
              day: 'numeric'
            }) : 'TBD';
          } else if (property === 'endDate') {
            return event.endDate ? new Date(event.endDate).toLocaleDateString('en-US', {
              year: 'numeric',
              month: 'long',
              day: 'numeric'
            }) : 'TBD';
          } else if (property === 'startTime') {
            return event.startTime || 'TBD';
          } else if (property === 'endTime') {
            return event.endTime || 'TBD';
          } else if (property === 'time') {
            return `${event.startTime || 'TBD'} - ${event.endTime || 'TBD'}`;
          } else if (property === 'category') {
            return event.category || 'General';
          } else if (property === 'type') {
            return event.eventType || 'PHYSICAL';
          } else if (property === 'status') {
            return event.status || 'Draft';
          } else if (property === 'url') {
            return `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/events/${event.id}`;
          }
        }

        // If we can't find the event or property, use the current event
        if (templateData.event && templateData.event[property]) {
          return templateData.event[property];
        }

        return match; // Keep the original placeholder if we can't resolve it
      }).catch(error => {
        console.error('Error fetching event:', error);

        // If there's an error, use the current event if available
        if (templateData.event && templateData.event[property]) {
          return templateData.event[property];
        }

        return match; // Keep the original placeholder if we can't resolve it
      });
    }

    // Standard variable processing
    const keys = path.trim().split('.');
    let value: any = templateData;

    // Navigate through the object path
    for (const key of keys) {
      if (value === undefined || value === null) {
        return match; // Keep the original placeholder if path is invalid
      }
      value = value[key];
    }

    // Return the value or the original placeholder if value is undefined
    return value !== undefined ? value : match;
  });
}

/**
 * Fetch real data for email templates
 * @param userId The user ID
 * @param eventId Optional event ID
 * @returns Promise with template data
 */
export async function fetchTemplateData(userId: string, eventId?: string): Promise<TemplateData> {
  // Initialize template data with defaults
  const templateData: TemplateData = {
    user: {
      name: 'User',
      email: '<EMAIL>'
    },
    organizer: {
      name: 'Organizer',
      email: '<EMAIL>'
    },
    date: new Date().toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  };

  try {
    // Fetch user data
    const user = await db.user.findUnique({
      where: { id: userId }
    });

    if (user) {
      templateData.user = {
        name: user.name || 'User',
        email: user.email || undefined,
        id: user.id,
        role: user.role
      };

      // If user is an organizer, use their data for organizer field
      if (user.role === 'ORGANIZER') {
        templateData.organizer = {
          name: user.name || 'Organizer',
          email: user.email || undefined,
          id: user.id
        };
      }
    }

    // If event ID is provided, fetch event data
    if (eventId) {
      try {
        console.log('Fetching event data for ID:', eventId);

        const event = await db.event.findUnique({
          where: { id: eventId },
          include: {
            tickets: true,
            orders: {
              take: 1,
              orderBy: { createdAt: 'desc' },
              include: {
                tickets: true
              }
            },
            user: true // Include the organizer
          }
        });

        if (event) {
          console.log('Found event:', event.title);

          // Format dates
          const startDate = event.startDate ? new Date(event.startDate) : new Date();
          const endDate = event.endDate ? new Date(event.endDate) : new Date();

          // Update event data
          templateData.event = {
            id: event.id,
            title: event.title || 'Event',
            date: startDate.toLocaleDateString('en-US', {
              year: 'numeric',
              month: 'long',
              day: 'numeric'
            }),
            startDate: startDate.toLocaleDateString('en-US', {
              year: 'numeric',
              month: 'long',
              day: 'numeric'
            }),
            endDate: endDate.toLocaleDateString('en-US', {
              year: 'numeric',
              month: 'long',
              day: 'numeric'
            }),
            time: `${event.startTime} - ${event.endTime}`,
            startTime: event.startTime,
            endTime: event.endTime,
            location: event.location || 'Event Location',
            venue: event.venue || 'Event Venue',
            description: event.description || 'Event Description',
            category: event.category || 'Event Category',
            type: event.eventType || 'PHYSICAL',
            status: event.status || 'Draft',
            url: `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/events/${event.id}`
          };

          // If we have an organizer, update the organizer data
          if (event.user) {
            templateData.organizer = {
              id: event.user.id,
              name: event.user.name || 'Event Organizer',
              email: event.user.email || undefined,
              role: event.user.role
            };
          }

          // Add ticket information if available
          if (event.tickets && event.tickets.length > 0) {
            console.log(`Found ${event.tickets.length} tickets`);
            const sampleTicket = event.tickets[0];

            templateData.ticket = {
              id: sampleTicket.id,
              type: sampleTicket.type || 'REGULAR',
              price: sampleTicket.price ? `$${sampleTicket.price}` : 'Free',
              quantity: sampleTicket.quantity || 1,
              isFree: sampleTicket.isFree,
              isAvailable: sampleTicket.isAvailable,
              saleStartTime: sampleTicket.saleStartTime,
              saleEndTime: sampleTicket.saleEndTime
            };
          }

          // Add order information if available
          if (event.orders && event.orders.length > 0) {
            console.log(`Found ${event.orders.length} orders`);
            const sampleOrder = event.orders[0];

            templateData.order = {
              id: sampleOrder.id,
              number: sampleOrder.id.substring(0, 8).toUpperCase(),
              date: new Date(sampleOrder.createdAt).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              }),
              total: sampleOrder.totalPrice ? `$${sampleOrder.totalPrice}` : '$0.00',
              status: sampleOrder.status,
              customerName: sampleOrder.customerName,
              customerEmail: sampleOrder.customerEmail,
              customerPhone: sampleOrder.customerPhone,
              paymentMethod: sampleOrder.paymentMethod || 'Credit Card'
            };

            // Add attendee information from the order
            templateData.attendee = {
              name: sampleOrder.customerName,
              email: sampleOrder.customerEmail,
              phone: sampleOrder.customerPhone
            };
          } else {
            // Create sample order data if none exists
            templateData.order = {
              number: `ORD-${Math.floor(Math.random() * 10000)}`,
              date: new Date().toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              }),
              total: '$99.99',
              status: 'Pending',
              customerName: 'Sample Customer',
              customerEmail: '<EMAIL>',
              customerPhone: '+1234567890',
              paymentMethod: 'Credit Card'
            };

            // Add sample attendee information
            templateData.attendee = {
              name: 'Sample Attendee',
              email: '<EMAIL>',
              phone: '+1234567890'
            };
          }
        } else {
          console.log('Event not found with ID:', eventId);
        }
      } catch (error) {
        console.error('Error fetching event data:', error);
      }
    }

    // Fetch subscribers for newsletter templates
    const subscribers = await db.newsletterSubscriber.findMany({
      where: { organizerId: userId },
      take: 5
    });

    if (subscribers.length > 0) {
      templateData.subscribers = subscribers.map(sub => ({
        email: sub.email,
        name: sub.firstName ? `${sub.firstName} ${sub.lastName || ''}`.trim() : 'Subscriber',
        id: sub.id
      }));

      // Add a sample subscriber for the subscriber variable
      templateData.subscriber = {
        email: subscribers[0].email,
        firstName: subscribers[0].firstName || 'Subscriber',
        lastName: subscribers[0].lastName || '',
        id: subscribers[0].id
      };
    } else {
      // No subscribers found, use empty array and sample data
      templateData.subscribers = [];
      templateData.subscriber = {
        email: '<EMAIL>',
        firstName: 'Sample',
        lastName: 'Subscriber',
        id: 'sample-id'
      };
    }

    return templateData;
  } catch (error) {
    console.error('Error fetching template data:', error);
    return templateData; // Return default data if there's an error
  }
}
