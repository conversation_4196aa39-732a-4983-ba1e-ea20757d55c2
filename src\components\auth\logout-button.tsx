'use client'

import { signOut } from 'next-auth/react';
import { useState } from 'react';

interface LogoutButtonProps {
  children?: React.ReactNode
}

export function LogoutButton({ children }: LogoutButtonProps) {
  const [isLoading, setIsLoading] = useState(false);

  async function onClick() {
    setIsLoading(true);
    try {
      // Use NextAuth's signOut function
      await signOut({
        callbackUrl: '/',
        redirect: true
      });
    } catch (error) {
      console.error('Logout error:', error);
      setIsLoading(false);
    }
  }

  return (
    <span
      onClick={onClick}
      className={`cursor-pointer ${isLoading ? 'opacity-50' : ''}`}
      style={{ pointerEvents: isLoading ? 'none' : 'auto' }}
    >
      {isLoading ? 'Logging out...' : children}
    </span>
  )
}