import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { compare } from 'bcryptjs';

export async function POST(request: NextRequest) {
  try {
    console.log('Test auth endpoint called');
    const body = await request.text();
    console.log('Request body:', body);

    let parsedBody;
    try {
      parsedBody = JSON.parse(body);
    } catch (parseError) {
      return NextResponse.json({
        success: false,
        error: 'Invalid JSON in request body',
        receivedBody: body
      }, { status: 400 });
    }

    const { email, password } = parsedBody;
    
    if (!email || !password) {
      return NextResponse.json({
        success: false,
        error: 'Email and password are required'
      }, { status: 400 });
    }
    
    // Find user by email
    const user = await db.user.findUnique({
      where: { email },
      select: {
        id: true,
        email: true,
        password: true,
        role: true,
        emailVerified: true,
        createdAt: true
      }
    });
    
    if (!user) {
      return NextResponse.json({
        success: false,
        error: 'User not found',
        email: email
      });
    }
    
    if (!user.password) {
      return NextResponse.json({
        success: false,
        error: 'User has no password (OAuth account?)',
        user: {
          id: user.id,
          email: user.email,
          role: user.role,
          emailVerified: user.emailVerified
        }
      });
    }
    
    // Test password comparison
    const isPasswordValid = await compare(password, user.password);
    
    return NextResponse.json({
      success: true,
      message: 'Authentication test completed',
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
        emailVerified: user.emailVerified,
        createdAt: user.createdAt
      },
      passwordValid: isPasswordValid,
      hasPassword: !!user.password
    });
    
  } catch (error) {
    console.error('Auth test error:', error);
    return NextResponse.json({
      success: false,
      error: 'Authentication test failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    success: true,
    message: 'Test auth endpoint is working',
    environment: {
      nodeEnv: process.env.NODE_ENV,
      hasDbUrl: !!process.env.DATABASE_URL,
      hasAuthSecret: !!process.env.AUTH_SECRET,
      nextAuthUrl: process.env.NEXTAUTH_URL,
      appUrl: process.env.NEXT_PUBLIC_APP_URL
    }
  });
}
