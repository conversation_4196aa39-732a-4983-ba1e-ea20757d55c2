import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { compare } from 'bcryptjs';

export async function POST(request: NextRequest) {
  try {
    const { email, password } = await request.json();
    
    if (!email || !password) {
      return NextResponse.json({
        success: false,
        error: 'Email and password are required'
      }, { status: 400 });
    }
    
    // Find user by email
    const user = await db.user.findUnique({
      where: { email },
      select: {
        id: true,
        email: true,
        password: true,
        role: true,
        emailVerified: true,
        createdAt: true
      }
    });
    
    if (!user) {
      return NextResponse.json({
        success: false,
        error: 'User not found',
        email: email
      });
    }
    
    if (!user.password) {
      return NextResponse.json({
        success: false,
        error: 'User has no password (OAuth account?)',
        user: {
          id: user.id,
          email: user.email,
          role: user.role,
          emailVerified: user.emailVerified
        }
      });
    }
    
    // Test password comparison
    const isPasswordValid = await compare(password, user.password);
    
    return NextResponse.json({
      success: true,
      message: 'Authentication test completed',
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
        emailVerified: user.emailVerified,
        createdAt: user.createdAt
      },
      passwordValid: isPasswordValid,
      hasPassword: !!user.password
    });
    
  } catch (error) {
    console.error('Auth test error:', error);
    return NextResponse.json({
      success: false,
      error: 'Authentication test failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
