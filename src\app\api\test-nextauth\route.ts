import { NextRequest, NextResponse } from 'next/server';

export async function GET() {
  try {
    // Test if NextAuth endpoints are accessible
    const baseUrl = process.env.NEXTAUTH_URL || process.env.NEXT_PUBLIC_APP_URL;
    
    const tests = [
      { name: 'providers', url: `${baseUrl}/api/auth/providers` },
      { name: 'csrf', url: `${baseUrl}/api/auth/csrf` },
      { name: 'session', url: `${baseUrl}/api/auth/session` }
    ];
    
    const results = [];
    
    for (const test of tests) {
      try {
        const response = await fetch(test.url);
        const isJson = response.headers.get('content-type')?.includes('application/json');
        
        let data = null;
        if (isJson) {
          try {
            data = await response.json();
          } catch (e) {
            data = 'Failed to parse JSON';
          }
        } else {
          data = await response.text();
        }
        
        results.push({
          name: test.name,
          url: test.url,
          status: response.status,
          ok: response.ok,
          contentType: response.headers.get('content-type'),
          data: data
        });
      } catch (error) {
        results.push({
          name: test.name,
          url: test.url,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }
    
    return NextResponse.json({
      success: true,
      message: 'NextAuth endpoint tests completed',
      baseUrl,
      results,
      environment: {
        nodeEnv: process.env.NODE_ENV,
        nextAuthUrl: process.env.NEXTAUTH_URL,
        appUrl: process.env.NEXT_PUBLIC_APP_URL,
        hasAuthSecret: !!process.env.AUTH_SECRET
      }
    });
    
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: 'Failed to test NextAuth endpoints',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
