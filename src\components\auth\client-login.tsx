'use client';

import { signIn } from 'next-auth/react';
import { useState } from 'react';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { FormError } from '../form-error';
import { FormSuccess } from '../form-success';
import { FcGoogle } from 'react-icons/fc';
import { FaGithub } from 'react-icons/fa';
import { Eye, EyeOff } from 'lucide-react';

export function ClientLogin() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setSuccess(null);
    setLoading(true);

    try {
      console.log('Attempting login with:', { email, hasPassword: !!password });

      const result = await signIn('credentials', {
        email,
        password,
        redirect: false,
      });

      console.log('SignIn result:', result);

      if (result?.error) {
        // Provide clearer error messages
        if (result.error === 'CredentialsSignin') {
          setError('Invalid email or password. Please check your credentials and try again.');
        } else if (result.error.includes('Email does not exist')) {
          setError('This email is not registered. Please check your email or create a new account.');
        } else if (result.error.includes('Confirmation email sent')) {
          setSuccess('Your email is not verified. We\'ve sent a new verification email to your inbox.');
        } else if (result.error.includes('Too many failed login attempts')) {
          setError(result.error);
        } else {
          setError(result.error);
        }
      } else if (result?.ok) {
        setSuccess('Login successful! Redirecting...');

        // Get the user session to determine their role
        const response = await fetch('/api/auth/session');

        // Check if response is ok and has content
        if (!response.ok) {
          throw new Error(`Session fetch failed: ${response.status} ${response.statusText}`);
        }

        const responseText = await response.text();
        if (!responseText) {
          throw new Error('Empty response from session endpoint');
        }

        let session;
        try {
          session = JSON.parse(responseText);
        } catch (parseError) {
          console.error('Failed to parse session response:', responseText);
          throw new Error('Invalid JSON response from session endpoint');
        }

        // Set the user-role cookie
        try {
          const cookieResponse = await fetch('/api/auth/set-role-cookie');
          if (!cookieResponse.ok) {
            console.warn(`Failed to set role cookie: ${cookieResponse.status}`);
          } else {
            console.log("Set user-role cookie");
          }
        } catch (error) {
          console.error("Error setting user-role cookie:", error);
          // Don't fail the login for cookie issues
        }

        // Redirect based on user role
        if (session?.user?.role) {
          switch (session.user.role) {
            case 'ADMIN':
              window.location.href = '/admin/dashboard';
              break;
            case 'USER':
              window.location.href = '/dashboard/user';
              break;
            case 'VENDOR':
              window.location.href = '/dashboard/vendor';
              break;
            case 'ORGANIZER':
              window.location.href = '/dashboard/organizer';
              break;
            case 'SUPERADMIN':
              window.location.href = '/admin/dashboard';
              break;
            case 'DEVELOPER':
              window.location.href = '/dashboard/developer';
              break;
            case 'PARTNER':
              window.location.href = '/dashboard/partner';
              break;
            default:
              // If user has unrecognized role, redirect to role selection
              window.location.href = '/auth/select-role';
              break;
          }
        } else {
          // If no role found, redirect to role selection
          window.location.href = '/auth/select-role';
        }
      }
    } catch (error) {
      console.error('Login error:', error);
      // Provide more specific error information
      if (error instanceof Error) {
        if (error.message.includes('JSON')) {
          setError('Server response error: The server returned an invalid response. This might be a temporary issue - please try again.');
        } else if (error.message.includes('fetch')) {
          setError('Network error: Unable to connect to the server. Please check your internet connection and try again.');
        } else if (error.message.includes('CORS')) {
          setError('Connection error: Please try refreshing the page and logging in again.');
        } else if (error.message.includes('timeout')) {
          setError('Request timeout: The server is taking too long to respond. Please try again.');
        } else if (error.message.includes('Session fetch failed')) {
          setError('Authentication error: Unable to verify your session. Please try logging in again.');
        } else if (error.message.includes('Empty response')) {
          setError('Server error: The authentication server returned an empty response. Please try again.');
        } else {
          setError(`Login failed: ${error.message}`);
        }
      } else {
        setError('An unexpected error occurred. Please try again or contact support if the problem persists.');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleSocialLogin = async (provider: string) => {
    setLoading(true);
    setError(null);

    try {
      console.log(`Starting ${provider} login...`);

      // Try to set the user-role cookie before redirecting
      // This may not work if the user isn't logged in yet, but we'll try anyway
      try {
        await fetch('/api/auth/set-role-cookie');
        console.log("Set user-role cookie for social login");
      } catch (error) {
        console.error("Error setting user-role cookie for social login:", error);
      }

      // Check if this is a partner login from the partners page
      const isPartnerLogin = window.location.pathname.includes('/partners') ||
                            window.location.search.includes('partner=true');

      // Use the standard NextAuth signIn function
      await signIn(provider, {
        callbackUrl: isPartnerLogin ?
          '/auth/select-role?partner=true' :
          '/api/auth/redirect' // Use our custom redirect handler
      });

      // Note: We don't need to handle the redirect manually as NextAuth will do it for us
    } catch (error) {
      console.error(`${provider} login error:`, error);
      setError(`Failed to login with ${provider}. Please try again.`);
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6 p-6 bg-white rounded-lg shadow-md w-full max-w-md">
      <div className="space-y-2 text-center">
        <h1 className="text-2xl font-bold">Login</h1>
        <p className="text-gray-500">Enter your credentials to access your account</p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="space-y-2">
          <label htmlFor="email" className="text-sm font-medium">
            Email
          </label>
          <Input
            id="email"
            type="email"
            placeholder="<EMAIL>"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
            disabled={loading}
          />
        </div>

        <div className="space-y-2">
          <label htmlFor="password" className="text-sm font-medium">
            Password
          </label>
          <div className="relative">
            <Input
              id="password"
              type={showPassword ? "text" : "password"}
              placeholder="********"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              disabled={loading}
              className="pr-10"
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-600"
              disabled={loading}
            >
              {showPassword ? (
                <EyeOff className="h-4 w-4" />
              ) : (
                <Eye className="h-4 w-4" />
              )}
            </button>
          </div>
          <div className="flex justify-end mt-1">
            <a href="/auth/reset" className="text-xs text-blue-600 hover:underline">
              Forgot password?
            </a>
          </div>
        </div>

        {error && <FormError message={error} />}
        {success && <FormSuccess message={success} />}

        <Button type="submit" className="w-full" disabled={loading}>
          {loading ? 'Logging in...' : 'Login'}
        </Button>
      </form>

      <div className="relative flex items-center justify-center my-4">
        <div className="border-t border-gray-300 w-full"></div>
        <div className="bg-white px-2 text-sm text-gray-500 absolute">or continue with</div>
      </div>

      <div className="grid grid-cols-2 gap-3">
        <Button
          variant="outline"
          type="button"
          onClick={() => handleSocialLogin('google')}
          disabled={loading}
          className="flex items-center justify-center w-full"
        >
          <FcGoogle className="mr-2" size={18} />
          Google
        </Button>
        <Button
          variant="outline"
          type="button"
          onClick={() => handleSocialLogin('github')}
          disabled={loading}
          className="flex items-center justify-center"
        >
          <FaGithub className="mr-2" size={18} />
          GitHub
        </Button>
      </div>

      <div className="text-center text-sm">
        <p>
          Don&apos;t have an account?{' '}
          <a href="/auth/register" className="text-blue-600 hover:underline">
            Register
          </a>
        </p>

      </div>
    </div>
  );
}
