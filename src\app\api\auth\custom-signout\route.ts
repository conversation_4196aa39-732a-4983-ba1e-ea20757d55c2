import { NextRequest, NextResponse } from 'next/server';

// Force this route to be dynamic
export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  // Get the CSRF token from the request cookies
  const csrfToken = request.cookies.get('next-auth.csrf-token')?.value?.split('|')[0] || '';

  // Get the base URL from the environment or use the IP address
  const baseUrl = process.env.NEXTAUTH_URL || process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';

  // Create a custom sign-out page with the correct form action
  const html = `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <style>
    :root {
      --border-width: 1px;
      --border-radius: 0.5rem;
      --color-error: #c94b4b;
      --color-info: #157efb;
      --color-info-hover: #0f6ddb;
      --color-info-text: #fff;
    }
    .__next-auth-theme-auto, .__next-auth-theme-light {
      --color-background: #ececec;
      --color-background-hover: hsla(0,0%,93%,.8);
      --color-background-card: #fff;
      --color-text: #000;
      --color-primary: #444;
      --color-control-border: #bbb;
      --color-button-active-background: #f9f9f9;
      --color-button-active-border: #aaa;
      --color-separator: #ccc;
    }
    .__next-auth-theme-dark {
      --color-background: #161b22;
      --color-background-hover: rgba(22,27,34,.8);
      --color-background-card: #0d1117;
      --color-text: #fff;
      --color-primary: #ccc;
      --color-control-border: #555;
      --color-button-active-background: #060606;
      --color-button-active-border: #666;
      --color-separator: #444;
    }
    @media (prefers-color-scheme: dark) {
      .__next-auth-theme-auto {
        --color-background: #161b22;
        --color-background-hover: rgba(22,27,34,.8);
        --color-background-card: #0d1117;
        --color-text: #fff;
        --color-primary: #ccc;
        --color-control-border: #555;
        --color-button-active-background: #060606;
        --color-button-active-border: #666;
        --color-separator: #444;
      }
    }
    html {
      box-sizing: border-box;
    }
    *, *:before, *:after {
      box-sizing: inherit;
      margin: 0;
      padding: 0;
    }
    body {
      background-color: var(--color-background);
      font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;
      margin: 0;
      padding: 0;
    }
    h1 {
      font-weight: 400;
      color: var(--color-text);
      margin-bottom: 1.5rem;
      padding: 0 1rem;
    }
    p {
      color: var(--color-text);
      margin-bottom: 1.5rem;
      padding: 0 1rem;
      font-size: 1.1rem;
      line-height: 2rem;
    }
    form {
      margin: 0;
      padding: 0;
    }
    button {
      align-items: center;
      background-color: var(--color-info);
      border-color: rgba(0,0,0,.1);
      border-radius: var(--border-radius);
      color: var(--color-info-text);
      display: flex;
      font-size: 1.1rem;
      font-weight: 500;
      justify-content: center;
      min-height: 62px;
      padding: .75rem 1rem;
      position: relative;
      transition: all .1s ease-in-out;
      width: 100%;
    }
    button:hover {
      background-color: var(--color-info-hover);
      cursor: pointer;
    }
    .page {
      box-sizing: border-box;
      display: grid;
      height: 100%;
      margin: 0;
      padding: 0;
      place-items: center;
      position: absolute;
      width: 100%;
    }
    .page > div {
      text-align: center;
    }
    .card {
      background-color: var(--color-background-card);
      border-radius: 2rem;
      padding: 1.25rem 2rem;
      margin: 2rem 0;
      width: 368px;
    }
    @media screen and (max-width: 450px) {
      .card {
        margin: 1rem 0;
        width: 343px;
      }
    }
  </style>
  <title>Sign Out</title>
</head>
<body class="__next-auth-theme-auto">
  <div class="page">
    <div class="signout">
      <div class="card">
        <h1>Sign Out</h1>
        <p>Are you sure you want to sign out?</p>
        <form action="${baseUrl}/api/auth/signout" method="POST">
          <input type="hidden" name="csrfToken" value="${csrfToken}" />
          <button id="submitButton" type="submit">Sign out</button>
        </form>
      </div>
    </div>
  </div>
</body>
</html>
  `;

  return new NextResponse(html, {
    headers: {
      'Content-Type': 'text/html',
    },
  });
}

export async function POST(request: NextRequest) {
  // Clear all cookies related to authentication
  const authCookies = [
    'next-auth.session-token',
    'next-auth.callback-url',
    'next-auth.csrf-token',
    '__Secure-next-auth.callback-url',
    '__Secure-next-auth.session-token',
    '__Secure-next-auth.csrf-token',
    '__Host-next-auth.csrf-token',
  ];

  const response = NextResponse.redirect(new URL('/', request.url));

  // Clear each cookie
  authCookies.forEach(cookieName => {
    response.cookies.delete(cookieName);
  });

  return response;
}
